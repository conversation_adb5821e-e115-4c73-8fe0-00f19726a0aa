@echo off
echo.
echo ========================================
echo    UpKeepPro Docker Setup Script
echo ========================================
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed or not in PATH
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not available
    echo Please ensure Docker Desktop is properly installed
    pause
    exit /b 1
)

echo ✅ Docker is installed and ready
echo.

REM Show menu
:menu
echo Choose an option:
echo.
echo 1. 🚀 Start Production Environment
echo 2. 🛠️  Start Development Environment  
echo 3. 📊 View Container Status
echo 4. 📋 View Logs
echo 5. 🛑 Stop All Containers
echo 6. 🧹 Clean Up Everything
echo 7. ❓ Show Help
echo 8. 🚪 Exit
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto production
if "%choice%"=="2" goto development
if "%choice%"=="3" goto status
if "%choice%"=="4" goto logs
if "%choice%"=="5" goto stop
if "%choice%"=="6" goto cleanup
if "%choice%"=="7" goto help
if "%choice%"=="8" goto exit
echo Invalid choice. Please try again.
goto menu

:production
echo.
echo 🚀 Starting Production Environment...
echo.
echo Building containers...
docker-compose build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    goto menu
)
echo.
echo Starting services...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    pause
    goto menu
)
echo.
echo ✅ Production environment started successfully!
echo.
echo 🌐 Access your application:
echo   Frontend: http://localhost:3000
echo   Backend:  http://localhost:5000
echo   MongoDB:  localhost:27017
echo   Mongo Express: http://localhost:8081 (admin/admin123)
echo.
pause
goto menu

:development
echo.
echo 🛠️ Starting Development Environment...
echo.
docker-compose -f docker-compose.dev.yml up -d
if %errorlevel% neq 0 (
    echo ❌ Failed to start development environment
    pause
    goto menu
)
echo.
echo ✅ Development environment started successfully!
echo.
echo 🌐 Access your application:
echo   Frontend: http://localhost:3000 (with hot reload)
echo   Backend:  http://localhost:5000 (with nodemon)
echo   MongoDB:  localhost:27017
echo   Mongo Express: http://localhost:8081 (admin/admin123)
echo.
pause
goto menu

:status
echo.
echo 📊 Container Status:
echo.
docker-compose ps
echo.
pause
goto menu

:logs
echo.
echo 📋 Container Logs (Press Ctrl+C to stop):
echo.
docker-compose logs -f
goto menu

:stop
echo.
echo 🛑 Stopping all containers...
echo.
docker-compose down
docker-compose -f docker-compose.dev.yml down
echo.
echo ✅ All containers stopped
echo.
pause
goto menu

:cleanup
echo.
echo ⚠️  This will remove all containers, volumes, and images
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto menu
echo.
echo 🧹 Cleaning up everything...
docker-compose down -v --rmi all
docker-compose -f docker-compose.dev.yml down -v --rmi all
docker system prune -f
echo.
echo ✅ Cleanup completed
echo.
pause
goto menu

:help
echo.
echo ❓ UpKeepPro Docker Help
echo.
echo Available commands:
echo   npm run docker:build     - Build all containers
echo   npm run docker:up        - Start production environment
echo   npm run docker:down      - Stop production environment
echo   npm run docker:dev       - Start development environment
echo   npm run docker:logs      - View logs
echo   npm run docker:status    - Check container status
echo   npm run docker:clean     - Clean up everything
echo.
echo Manual Docker commands:
echo   docker-compose ps                    - Show container status
echo   docker-compose logs -f               - Follow logs
echo   docker-compose exec backend sh       - Access backend container
echo   docker-compose exec mongodb mongosh  - Access MongoDB shell
echo.
echo Troubleshooting:
echo   - If ports are in use, stop other services first
echo   - If build fails, try: docker system prune -a
echo   - Check Docker Desktop is running
echo   - Ensure no other instances are running
echo.
pause
goto menu

:exit
echo.
echo 👋 Goodbye!
echo.
exit /b 0
