version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: upkeeppro-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: upkeeppro
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - upkeeppro-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API Server
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: upkeeppro-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGO_URI: ********************************************************************
      JWT_SECRET: ti2v3uZardPlOuoVie4k/D9KNMEH4S9DWFR/Oi+O114=
      FRONTEND_URL: http://localhost:3000
    ports:
      - "5000:5000"
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - upkeeppro-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/users/stats"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: ../docker/Dockerfile.frontend
    container_name: upkeeppro-frontend
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:5000
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - upkeeppro-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    build:
      context: ./docker
      dockerfile: Dockerfile.nginx
    container_name: upkeeppro-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - upkeeppro-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Ollama AI Service (Optional - for AI chatbot)
  ollama:
    image: ollama/ollama:latest
    container_name: upkeeppro-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - upkeeppro-network
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/version"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s

volumes:
  mongodb_data:
    driver: local
  ollama_data:
    driver: local

networks:
  upkeeppro-network:
    driver: bridge
